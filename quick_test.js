/**
 * 快速库存测试脚本
 * 直接在浏览器控制台中运行
 * 
 * 使用方法：
 * 1. 打开商品详情页：standard/product/detail?productId=200002310010&goodsId=100002140037&u=MTY1
 * 2. 按F12打开开发者工具，切换到Console标签
 * 3. 复制粘贴下面的代码并按回车运行
 */

// 快速测试函数
(function quickStockTest() {
  console.log('🚀 开始快速库存测试');
  
  // 尝试获取页面实例
  let pageInstance = null;
  
  // 方法1: getCurrentPages (uni-app)
  try {
    if (typeof getCurrentPages === 'function') {
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        pageInstance = pages[pages.length - 1];
        console.log('✅ 通过getCurrentPages获取到页面实例');
      }
    }
  } catch (e) {
    console.log('⚠️ getCurrentPages方法不可用');
  }
  
  // 方法2: 通过DOM查找Vue实例
  if (!pageInstance) {
    try {
      const appEl = document.querySelector('#app');
      if (appEl && appEl.__vue__) {
        pageInstance = appEl.__vue__;
        console.log('✅ 通过DOM查找获取到Vue实例');
      }
    } catch (e) {
      console.log('⚠️ DOM查找方法失败');
    }
  }
  
  // 方法3: 查找所有可能的Vue实例
  if (!pageInstance) {
    try {
      const allElements = document.querySelectorAll('*');
      for (let el of allElements) {
        if (el.__vue__ && (el.__vue__.goodsData || el.__vue__.defaultProduct)) {
          pageInstance = el.__vue__;
          console.log('✅ 通过遍历DOM获取到商品页面实例');
          break;
        }
      }
    } catch (e) {
      console.log('⚠️ 遍历DOM方法失败');
    }
  }
  
  if (!pageInstance) {
    console.error('❌ 无法获取页面实例，请确保在商品详情页运行此脚本');
    return;
  }
  
  // 显示当前状态
  console.log('📊 当前商品状态:');
  console.log('商品名称:', pageInstance.goodsData?.goodsName || '未知');
  console.log('当前库存:', pageInstance.defaultProduct?.productStock || 0);
  console.log('规格数量:', pageInstance.specs?.length || 0);
  console.log('hasAnyStock:', pageInstance.hasAnyStock);
  
  // 备份原始数据
  const originalData = {
    defaultProduct: pageInstance.defaultProduct ? JSON.parse(JSON.stringify(pageInstance.defaultProduct)) : {},
    specs: pageInstance.specs ? JSON.parse(JSON.stringify(pageInstance.specs)) : []
  };
  
  console.log('💾 原始数据已备份');
  
  // 测试场景1：部分SKU有库存
  console.log('\n🧪 测试场景1：部分SKU有库存');
  
  // 设置当前SKU库存为0
  if (!pageInstance.defaultProduct) pageInstance.defaultProduct = {};
  pageInstance.defaultProduct.productStock = 0;
  
  // 设置规格数据
  pageInstance.specs = [
    {
      specId: 1,
      specName: "活动时间",
      specValueList: [
        {
          specValueId: 11,
          specValue: "周一晚上",
          checkState: "1", // 当前选中，库存为0
          image: ""
        },
        {
          specValueId: 12,
          specValue: "周二晚上",
          checkState: "2", // 可选，有库存
          image: ""
        }
      ]
    }
  ];
  
  // 强制更新
  if (pageInstance.$forceUpdate) {
    pageInstance.$forceUpdate();
  }
  
  console.log('当前库存:', pageInstance.defaultProduct.productStock);
  console.log('hasAnyStock:', pageInstance.hasAnyStock);
  console.log('预期: hasAnyStock应该为true（因为有可选规格）');
  console.log('结果:', pageInstance.hasAnyStock === true ? '✅ 正确' : '❌ 错误');
  
  // 等待3秒后测试场景2
  setTimeout(() => {
    console.log('\n🧪 测试场景2：所有SKU都无库存');
    
    // 设置所有规格为禁用
    pageInstance.specs[0].specValueList[1].checkState = "3"; // 禁用第二个规格
    
    if (pageInstance.$forceUpdate) {
      pageInstance.$forceUpdate();
    }
    
    console.log('当前库存:', pageInstance.defaultProduct.productStock);
    console.log('hasAnyStock:', pageInstance.hasAnyStock);
    console.log('预期: hasAnyStock应该为false（没有可选规格且当前库存为0）');
    console.log('结果:', pageInstance.hasAnyStock === false ? '✅ 正确' : '❌ 错误');
    
    // 等待3秒后恢复原始数据
    setTimeout(() => {
      console.log('\n🔄 恢复原始数据');
      pageInstance.defaultProduct = originalData.defaultProduct;
      pageInstance.specs = originalData.specs;
      
      if (pageInstance.$forceUpdate) {
        pageInstance.$forceUpdate();
      }
      
      console.log('✅ 测试完成，数据已恢复');
      console.log('当前库存:', pageInstance.defaultProduct?.productStock || 0);
      console.log('hasAnyStock:', pageInstance.hasAnyStock);
      
      // 提供手动测试函数
      window.manualTest = {
        // 手动设置库存为0
        setStockZero: () => {
          if (pageInstance.defaultProduct) {
            pageInstance.defaultProduct.productStock = 0;
            if (pageInstance.$forceUpdate) pageInstance.$forceUpdate();
            console.log('✅ 已设置库存为0');
          }
        },
        
        // 手动恢复库存
        restoreStock: () => {
          pageInstance.defaultProduct = originalData.defaultProduct;
          if (pageInstance.$forceUpdate) pageInstance.$forceUpdate();
          console.log('✅ 已恢复原始库存');
        },
        
        // 查看当前状态
        checkStatus: () => {
          console.log('当前库存:', pageInstance.defaultProduct?.productStock || 0);
          console.log('hasAnyStock:', pageInstance.hasAnyStock);
          console.log('规格数量:', pageInstance.specs?.length || 0);
        }
      };
      
      console.log('\n💡 手动测试函数已创建:');
      console.log('manualTest.setStockZero() - 设置库存为0');
      console.log('manualTest.restoreStock() - 恢复原始库存');
      console.log('manualTest.checkStatus() - 查看当前状态');
      
    }, 3000);
  }, 3000);
  
})();

console.log('📝 测试说明:');
console.log('1. 脚本会自动运行两个测试场景');
console.log('2. 每个场景间隔3秒');
console.log('3. 最后会恢复原始数据');
console.log('4. 观察页面上底部按钮和规格选择的变化');
console.log('5. 检查控制台输出的hasAnyStock值是否正确');
