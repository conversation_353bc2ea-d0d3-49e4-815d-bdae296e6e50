/**
 * SKU库存测试脚本
 * 用于验证修复后的SKU库存逻辑是否正常工作
 */

// 模拟测试数据
const testScenarios = {
  // 场景1：部分SKU有库存
  partialStock: {
    goodsData: {
      goodsId: 12345,
      goodsName: "测试商品 - 部分SKU有库存",
      state: 3, // 商品上架
      isVirtualGoods: 1
    },
    defaultProduct: {
      productId: 67890,
      productStock: 0, // 当前选中SKU无库存
      productPrice: 99.00,
      goodsPics: ["image1.jpg", "image2.jpg"]
    },
    specs: [
      {
        specId: 1,
        specName: "颜色",
        specValueList: [
          {
            specValueId: 11,
            specValue: "红色",
            checkState: "1", // 当前选中，但无库存
            image: ""
          },
          {
            specValueId: 12,
            specValue: "蓝色",
            checkState: "2", // 可选，有库存
            image: ""
          },
          {
            specValueId: 13,
            specValue: "绿色",
            checkState: "3", // 禁用，无库存
            image: ""
          }
        ]
      },
      {
        specId: 2,
        specName: "尺寸",
        specValueList: [
          {
            specValueId: 21,
            specValue: "S",
            checkState: "1", // 当前选中
            image: ""
          },
          {
            specValueId: 22,
            specValue: "M",
            checkState: "2", // 可选
            image: ""
          }
        ]
      }
    ]
  },

  // 场景2：所有SKU都无库存
  noStock: {
    goodsData: {
      goodsId: 12346,
      goodsName: "测试商品 - 无库存",
      state: 3,
      isVirtualGoods: 1
    },
    defaultProduct: {
      productId: 67891,
      productStock: 0,
      productPrice: 99.00,
      goodsPics: ["image1.jpg"]
    },
    specs: [
      {
        specId: 1,
        specName: "颜色",
        specValueList: [
          {
            specValueId: 11,
            specValue: "红色",
            checkState: "1", // 当前选中，无库存
            image: ""
          },
          {
            specValueId: 12,
            specValue: "蓝色",
            checkState: "3", // 禁用，无库存
            image: ""
          }
        ]
      }
    ]
  },

  // 场景3：单SKU商品无库存
  singleSkuNoStock: {
    goodsData: {
      goodsId: 12347,
      goodsName: "测试商品 - 单SKU无库存",
      state: 3,
      isVirtualGoods: 1
    },
    defaultProduct: {
      productId: 67892,
      productStock: 0,
      productPrice: 99.00,
      goodsPics: ["image1.jpg"]
    },
    specs: [] // 无规格
  }
};

// 测试函数
function testHasAnyStock(scenario) {
  const { specs, defaultProduct } = scenario;

  // 模拟 hasAnyStock 计算属性的逻辑
  // 如果没有规格，直接返回当前产品的库存状态
  if (!specs || specs.length === 0) {
    return defaultProduct && defaultProduct.productStock > 0;
  }

  // 如果有规格，需要检查是否有任何可选的规格组合
  // checkState: 1-选中，2-可选，3-禁用
  // 如果当前选中的规格有库存，或者有其他可选的规格，就说明有库存
  const hasAvailableSpecs = specs.some(spec =>
    spec.specValueList.some(value => value.checkState === '2')
  );

  // 如果当前选中的规格有库存，也算有库存
  const currentHasStock = defaultProduct && defaultProduct.productStock > 0;

  return hasAvailableSpecs || currentHasStock;
}

// 运行测试
function runTests() {
  console.log('=== SKU库存测试开始 ===\n');

  Object.keys(testScenarios).forEach(scenarioName => {
    const scenario = testScenarios[scenarioName];
    const hasStock = testHasAnyStock(scenario);
    
    console.log(`场景: ${scenarioName}`);
    console.log(`商品名称: ${scenario.goodsData.goodsName}`);
    console.log(`当前SKU库存: ${scenario.defaultProduct.productStock}`);
    console.log(`是否有可用库存: ${hasStock ? '是' : '否'}`);
    
    // 预期结果验证
    let expectedResult;
    switch(scenarioName) {
      case 'partialStock':
        expectedResult = true; // 应该有库存（有可选的规格）
        break;
      case 'noStock':
        expectedResult = false; // 应该无库存（所有规格都禁用）
        break;
      case 'singleSkuNoStock':
        expectedResult = false; // 应该无库存（单SKU且库存为0）
        break;
    }
    
    const testPassed = hasStock === expectedResult;
    console.log(`测试结果: ${testPassed ? '✅ 通过' : '❌ 失败'}`);
    
    if (!testPassed) {
      console.log(`预期: ${expectedResult}, 实际: ${hasStock}`);
    }
    
    console.log('---\n');
  });

  console.log('=== 测试完成 ===');
}

// 模拟用户操作测试
function simulateUserActions() {
  console.log('=== 用户操作模拟测试 ===\n');
  
  const scenario = testScenarios.partialStock;
  console.log('模拟场景：部分SKU有库存');
  console.log('1. 用户进入商品详情页');
  console.log(`   - 当前选中SKU库存: ${scenario.defaultProduct.productStock}`);
  console.log(`   - 底部按钮应显示: ${testHasAnyStock(scenario) ? '正常购买按钮' : '库存不足'}`);
  
  console.log('2. 用户点击规格选择');
  console.log('   - 规格弹框应正常打开');
  console.log('   - 当前选中规格应显示: "当前规格库存不足，请选择其他规格"');
  
  console.log('3. 用户选择其他有库存的规格');
  console.log('   - 应该可以正常选择蓝色规格');
  console.log('   - 选择后应更新产品信息和库存状态');
  
  console.log('4. 用户尝试购买');
  console.log('   - 应该可以正常加入购物车或立即购买');
  
  console.log('\n=== 用户操作模拟完成 ===');
}

// 执行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = { runTests, simulateUserActions, testScenarios };
  // 如果直接运行此文件，执行测试
  if (require.main === module) {
    runTests();
    simulateUserActions();
  }
} else {
  // 浏览器环境
  runTests();
  simulateUserActions();
}
