/**
 * uni-app 商品库存模拟器
 * 专门用于测试商品详情页的库存逻辑
 * 
 * 使用步骤：
 * 1. 在微信开发者工具或浏览器中打开商品详情页
 * 2. 在控制台中粘贴并运行此脚本
 * 3. 使用提供的函数进行测试
 */

(function() {
  'use strict';
  
  console.log('🔧 uni-app 库存模拟器已加载');
  
  // 获取当前页面实例的多种方法
  function getCurrentPage() {
    try {
      // 方法1: uni-app的getCurrentPages
      if (typeof getCurrentPages === 'function') {
        const pages = getCurrentPages();
        if (pages && pages.length > 0) {
          return pages[pages.length - 1];
        }
      }
      
      // 方法2: 通过全局变量
      if (window.getApp) {
        const app = window.getApp();
        if (app && app.globalData && app.globalData.currentPage) {
          return app.globalData.currentPage;
        }
      }
      
      // 方法3: 通过DOM查找Vue实例
      const appElement = document.querySelector('#app');
      if (appElement && appElement.__vue__) {
        return appElement.__vue__;
      }
      
      return null;
    } catch (error) {
      console.error('获取页面实例失败:', error);
      return null;
    }
  }
  
  // 查找商品详情页的Vue实例
  function findProductDetailInstance() {
    const page = getCurrentPage();
    if (!page) {
      console.error('❌ 无法获取当前页面实例');
      return null;
    }
    
    // 检查是否是商品详情页
    if (page.$route && page.$route.path && page.$route.path.includes('product/detail')) {
      return page;
    }
    
    // 检查页面数据是否包含商品信息
    if (page.goodsData || page.defaultProduct || page.specs) {
      return page;
    }
    
    console.error('❌ 当前页面不是商品详情页或无法识别');
    return null;
  }
  
  // 显示当前商品信息
  function showCurrentProductInfo() {
    console.log('📋 当前商品信息：');
    
    const instance = findProductDetailInstance();
    if (!instance) return;
    
    console.log('商品名称:', instance.goodsData?.goodsName || '未知');
    console.log('商品ID:', instance.goodsId || '未知');
    console.log('产品ID:', instance.productId || '未知');
    console.log('当前库存:', instance.defaultProduct?.productStock || 0);
    console.log('规格数量:', instance.specs?.length || 0);
    
    if (instance.specs && instance.specs.length > 0) {
      console.log('规格信息:');
      instance.specs.forEach((spec, i) => {
        console.log(`  ${i+1}. ${spec.specName}:`);
        spec.specValueList.forEach(value => {
          const state = value.checkState === '1' ? '✅选中' : 
                       value.checkState === '2' ? '⭕可选' : '❌禁用';
          console.log(`     ${value.specValue}: ${state}`);
        });
      });
    }
    
    // 检查hasAnyStock计算属性
    if (typeof instance.hasAnyStock !== 'undefined') {
      console.log('hasAnyStock:', instance.hasAnyStock);
    }
  }
  
  // 模拟场景1：部分SKU有库存
  function testPartialStock() {
    console.log('🧪 测试场景：部分SKU有库存');
    
    const instance = findProductDetailInstance();
    if (!instance) return null;
    
    // 备份原始数据
    const backup = {
      defaultProduct: JSON.parse(JSON.stringify(instance.defaultProduct || {})),
      specs: JSON.parse(JSON.stringify(instance.specs || []))
    };
    
    // 设置当前SKU库存为0
    if (!instance.defaultProduct) instance.defaultProduct = {};
    instance.defaultProduct.productStock = 0;
    
    // 创建或修改规格数据
    instance.specs = [
      {
        specId: 1,
        specName: "活动时间",
        specValueList: [
          {
            specValueId: 11,
            specValue: "周一晚上",
            checkState: "1", // 当前选中，库存为0
            image: ""
          },
          {
            specValueId: 12,
            specValue: "周二晚上",
            checkState: "2", // 可选，有库存
            image: ""
          },
          {
            specValueId: 13,
            specValue: "周三晚上", 
            checkState: "2", // 可选，有库存
            image: ""
          }
        ]
      }
    ];
    
    console.log('✅ 模拟完成');
    console.log('预期结果:');
    console.log('- 底部按钮应显示正常购买选项');
    console.log('- 规格弹框应显示"当前规格库存不足，请选择其他规格"');
    console.log('- 用户可以选择其他规格');
    
    // 强制更新视图
    if (instance.$forceUpdate) {
      instance.$forceUpdate();
    }
    
    return function restore() {
      instance.defaultProduct = backup.defaultProduct;
      instance.specs = backup.specs;
      if (instance.$forceUpdate) {
        instance.$forceUpdate();
      }
      console.log('🔄 已恢复原始数据');
    };
  }
  
  // 模拟场景2：所有SKU都无库存
  function testNoStock() {
    console.log('🧪 测试场景：所有SKU都无库存');
    
    const instance = findProductDetailInstance();
    if (!instance) return null;
    
    // 备份原始数据
    const backup = {
      defaultProduct: JSON.parse(JSON.stringify(instance.defaultProduct || {})),
      specs: JSON.parse(JSON.stringify(instance.specs || []))
    };
    
    // 设置当前SKU库存为0
    if (!instance.defaultProduct) instance.defaultProduct = {};
    instance.defaultProduct.productStock = 0;
    
    // 设置所有规格都为禁用状态
    instance.specs = [
      {
        specId: 1,
        specName: "活动时间",
        specValueList: [
          {
            specValueId: 11,
            specValue: "周一晚上",
            checkState: "1", // 当前选中，但无库存
            image: ""
          },
          {
            specValueId: 12,
            specValue: "周二晚上",
            checkState: "3", // 禁用，无库存
            image: ""
          },
          {
            specValueId: 13,
            specValue: "周三晚上",
            checkState: "3", // 禁用，无库存
            image: ""
          }
        ]
      }
    ];
    
    console.log('✅ 模拟完成');
    console.log('预期结果:');
    console.log('- 底部按钮应显示"库存不足"');
    console.log('- 规格弹框应显示"库存不足"');
    console.log('- 所有规格选项都应显示为禁用状态');
    
    // 强制更新视图
    if (instance.$forceUpdate) {
      instance.$forceUpdate();
    }
    
    return function restore() {
      instance.defaultProduct = backup.defaultProduct;
      instance.specs = backup.specs;
      if (instance.$forceUpdate) {
        instance.$forceUpdate();
      }
      console.log('🔄 已恢复原始数据');
    };
  }
  
  // 模拟场景3：单SKU无库存
  function testSingleSkuNoStock() {
    console.log('🧪 测试场景：单SKU商品无库存');
    
    const instance = findProductDetailInstance();
    if (!instance) return null;
    
    // 备份原始数据
    const backup = {
      defaultProduct: JSON.parse(JSON.stringify(instance.defaultProduct || {})),
      specs: JSON.parse(JSON.stringify(instance.specs || []))
    };
    
    // 设置当前SKU库存为0
    if (!instance.defaultProduct) instance.defaultProduct = {};
    instance.defaultProduct.productStock = 0;
    
    // 清空规格（单SKU商品）
    instance.specs = [];
    
    console.log('✅ 模拟完成');
    console.log('预期结果:');
    console.log('- 底部按钮应显示"库存不足"');
    console.log('- 没有规格选择选项');
    
    // 强制更新视图
    if (instance.$forceUpdate) {
      instance.$forceUpdate();
    }
    
    return function restore() {
      instance.defaultProduct = backup.defaultProduct;
      instance.specs = backup.specs;
      if (instance.$forceUpdate) {
        instance.$forceUpdate();
      }
      console.log('🔄 已恢复原始数据');
    };
  }
  
  // 检查hasAnyStock计算属性的值
  function checkHasAnyStock() {
    const instance = findProductDetailInstance();
    if (!instance) return;
    
    console.log('🔍 检查 hasAnyStock 计算属性:');
    
    if (typeof instance.hasAnyStock !== 'undefined') {
      console.log('hasAnyStock 值:', instance.hasAnyStock);
      
      // 手动计算预期值进行对比
      let expectedValue;
      if (!instance.specs || instance.specs.length === 0) {
        expectedValue = instance.defaultProduct && instance.defaultProduct.productStock > 0;
      } else {
        const hasAvailableSpecs = instance.specs.some(spec => 
          spec.specValueList.some(value => value.checkState === '2')
        );
        const currentHasStock = instance.defaultProduct && instance.defaultProduct.productStock > 0;
        expectedValue = hasAvailableSpecs || currentHasStock;
      }
      
      console.log('预期值:', expectedValue);
      console.log('结果:', instance.hasAnyStock === expectedValue ? '✅ 正确' : '❌ 错误');
    } else {
      console.log('❌ hasAnyStock 计算属性不存在');
    }
  }
  
  // 导出到全局
  window.stockSimulator = {
    showInfo: showCurrentProductInfo,
    testPartialStock,
    testNoStock, 
    testSingleSkuNoStock,
    checkHasAnyStock
  };
  
  // 显示使用说明
  console.log('📖 使用说明:');
  console.log('stockSimulator.showInfo() - 显示当前商品信息');
  console.log('stockSimulator.testPartialStock() - 测试部分SKU有库存');
  console.log('stockSimulator.testNoStock() - 测试所有SKU无库存');
  console.log('stockSimulator.testSingleSkuNoStock() - 测试单SKU无库存');
  console.log('stockSimulator.checkHasAnyStock() - 检查hasAnyStock值');
  console.log('');
  console.log('💡 测试流程:');
  console.log('1. stockSimulator.showInfo() // 查看当前状态');
  console.log('2. const restore = stockSimulator.testPartialStock() // 运行测试');
  console.log('3. // 观察页面变化，测试功能');
  console.log('4. restore() // 恢复原始数据');
  
})();
