# SKU库存不足问题修复测试计划

## 问题描述
当某个SKU库存为0时，整个商品的购买按钮都会显示"库存不足"，用户无法切换到其他有库存的SKU进行购买。

## 修复内容

### 1. 核心修复
- 添加了 `hasAnyStock` 计算属性，用于判断是否所有SKU都没有库存
- 修改了底部操作按钮的显示逻辑，从 `defaultProduct.productStock == 0` 改为 `!hasAnyStock`
- 修改了规格弹框内按钮的逻辑，区分"当前规格库存不足"和"所有规格都库存不足"

### 2. 用户体验改进
- 为规格选项添加了库存状态的可视化指示
- 当前选中的SKU库存为0时，显示"当前规格库存不足，请选择其他规格"
- 禁用状态的规格显示"无库存"提示

### 3. 修改的文件
- `sld-mobile/src/standard/product/detail.vue`

## 测试场景

### 场景1：部分SKU有库存
**前置条件：**
- 商品有多个SKU（如：颜色、尺寸）
- 部分SKU库存为0，部分SKU有库存
- 默认选中的SKU库存为0

**测试步骤：**
1. 进入商品详情页
2. 检查底部操作按钮状态
3. 点击底部按钮打开规格选择弹框
4. 检查规格选项的显示状态
5. 选择有库存的SKU
6. 检查价格、库存信息更新
7. 尝试加入购物车/立即购买

**预期结果：**
- 底部按钮正常显示（不显示"库存不足"）
- 规格弹框正常打开
- 当前选中的无库存SKU显示"当前规格库存不足，请选择其他规格"
- 可以正常选择其他有库存的SKU
- 选择有库存SKU后，可以正常购买

### 场景2：所有SKU都无库存
**前置条件：**
- 商品有多个SKU
- 所有SKU库存都为0

**测试步骤：**
1. 进入商品详情页
2. 检查底部操作按钮状态
3. 点击底部按钮
4. 检查规格选项状态

**预期结果：**
- 底部按钮显示"库存不足"
- 点击按钮可以打开规格弹框（用于查看规格信息）
- 规格弹框内显示"库存不足"
- 所有规格选项都显示为禁用状态

### 场景3：单SKU商品
**前置条件：**
- 商品只有一个SKU
- SKU库存为0

**测试步骤：**
1. 进入商品详情页
2. 检查底部操作按钮状态

**预期结果：**
- 底部按钮显示"库存不足"
- 行为与修复前一致

### 场景4：活动商品（秒杀、拼团等）
**前置条件：**
- 商品参与活动（秒杀、预售、拼团、阶梯团）
- 部分SKU库存为0

**测试步骤：**
1. 进入商品详情页
2. 检查活动信息显示
3. 检查底部按钮和规格选择逻辑

**预期结果：**
- 活动信息正常显示
- 库存逻辑与普通商品一致
- 当前规格无库存时显示对应的提示（如"当前规格已抢完，请选择其他规格"）

## 测试数据准备

### 模拟数据结构
```javascript
// 示例：有库存的多SKU商品
const mockGoodsData = {
  goodsId: 12345,
  goodsName: "测试商品",
  defaultProduct: {
    productId: 67890,
    productStock: 0, // 默认SKU库存为0
    productPrice: 99.00
  },
  specs: [
    {
      specId: 1,
      specName: "颜色",
      specValueList: [
        {
          specValueId: 11,
          specValue: "红色",
          checkState: "1", // 当前选中
          image: ""
        },
        {
          specValueId: 12,
          specValue: "蓝色", 
          checkState: "2", // 可选
          image: ""
        },
        {
          specValueId: 13,
          specValue: "绿色",
          checkState: "3", // 禁用（无库存）
          image: ""
        }
      ]
    }
  ]
}
```

## 验证要点

1. **库存判断逻辑**：确保 `hasAnyStock` 计算属性正确判断
2. **按钮状态**：底部按钮根据整体库存状态显示
3. **规格选择**：用户可以正常切换到有库存的SKU
4. **用户提示**：清晰的库存状态提示
5. **购买流程**：选择有库存SKU后可以正常购买
6. **兼容性**：确保各种活动类型的商品都正常工作

## 回归测试

确保修复不影响以下功能：
- 正常的SKU切换逻辑
- 价格更新机制
- 图片切换功能
- 活动商品的特殊逻辑
- 购物车添加功能
- 立即购买功能
