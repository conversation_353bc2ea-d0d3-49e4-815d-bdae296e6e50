/**
 * 前端模拟库存测试脚本
 * 用于在浏览器控制台中模拟不同的库存情况
 * 
 * 使用方法：
 * 1. 打开商品详情页：standard/product/detail?productId=200002310010&goodsId=100002140037&u=MTY1
 * 2. 打开浏览器控制台
 * 3. 复制粘贴此脚本并运行
 * 4. 调用相应的测试函数
 */

// 等待Vue实例加载完成
function waitForVue(callback) {
  const checkVue = () => {
    // 尝试多种方式获取Vue实例
    const vueInstance = window.Vue || 
                       (window.__VUE__ && window.__VUE__.default) ||
                       (document.querySelector('#app') && document.querySelector('#app').__vue__) ||
                       (window.getApp && window.getApp().$children && window.getApp().$children[0]);
    
    if (vueInstance) {
      callback(vueInstance);
    } else {
      setTimeout(checkVue, 100);
    }
  };
  checkVue();
}

// 获取当前页面的Vue实例
function getCurrentPageVue() {
  // uni-app的页面实例获取方式
  const pages = getCurrentPages();
  if (pages && pages.length > 0) {
    return pages[pages.length - 1];
  }
  
  // 尝试其他方式
  return document.querySelector('#app').__vue__ || window.getApp().$children[0];
}

// 模拟场景1：当前SKU库存为0，但有其他可选SKU
function simulatePartialStock() {
  console.log('🧪 开始模拟：部分SKU有库存的情况');
  
  try {
    const pageInstance = getCurrentPageVue();
    if (!pageInstance) {
      console.error('❌ 无法获取页面Vue实例');
      return;
    }

    // 备份原始数据
    const originalDefaultProduct = JSON.parse(JSON.stringify(pageInstance.defaultProduct));
    const originalSpecs = JSON.parse(JSON.stringify(pageInstance.specs));
    
    console.log('📦 原始数据已备份');
    console.log('原始库存:', originalDefaultProduct.productStock);

    // 模拟当前选中SKU库存为0
    pageInstance.defaultProduct.productStock = 0;
    
    // 模拟有多个规格，其中一些有库存
    if (!pageInstance.specs || pageInstance.specs.length === 0) {
      // 如果原本没有规格，创建模拟规格
      pageInstance.specs = [
        {
          specId: 1,
          specName: "时间段",
          specValueList: [
            {
              specValueId: 11,
              specValue: "周一晚上",
              checkState: "1", // 当前选中，但库存为0
              image: ""
            },
            {
              specValueId: 12,
              specValue: "周二晚上", 
              checkState: "2", // 可选，有库存
              image: ""
            },
            {
              specValueId: 13,
              specValue: "周三晚上",
              checkState: "2", // 可选，有库存
              image: ""
            }
          ]
        }
      ];
    } else {
      // 如果已有规格，修改其状态
      pageInstance.specs.forEach(spec => {
        spec.specValueList.forEach((value, index) => {
          if (value.checkState === '1') {
            // 当前选中的设为库存不足（但仍可选择）
            console.log(`设置 ${value.specValue} 为当前选中但库存不足`);
          } else if (index < spec.specValueList.length - 1) {
            // 其他规格设为可选（有库存）
            value.checkState = '2';
            console.log(`设置 ${value.specValue} 为可选状态`);
          }
        });
      });
    }

    console.log('✅ 模拟完成！');
    console.log('📊 当前状态：');
    console.log('- 当前SKU库存:', pageInstance.defaultProduct.productStock);
    console.log('- hasAnyStock 应该返回:', pageInstance.hasAnyStock);
    console.log('- 底部按钮应该显示正常购买按钮');
    console.log('- 规格弹框应该显示"当前规格库存不足，请选择其他规格"');
    
    // 返回恢复函数
    return function restore() {
      pageInstance.defaultProduct = originalDefaultProduct;
      pageInstance.specs = originalSpecs;
      console.log('🔄 已恢复原始数据');
    };
    
  } catch (error) {
    console.error('❌ 模拟失败:', error);
  }
}

// 模拟场景2：所有SKU都没有库存
function simulateNoStock() {
  console.log('🧪 开始模拟：所有SKU都没有库存的情况');
  
  try {
    const pageInstance = getCurrentPageVue();
    if (!pageInstance) {
      console.error('❌ 无法获取页面Vue实例');
      return;
    }

    // 备份原始数据
    const originalDefaultProduct = JSON.parse(JSON.stringify(pageInstance.defaultProduct));
    const originalSpecs = JSON.parse(JSON.stringify(pageInstance.specs));
    
    // 设置当前SKU库存为0
    pageInstance.defaultProduct.productStock = 0;
    
    // 设置所有规格都为禁用状态
    if (pageInstance.specs && pageInstance.specs.length > 0) {
      pageInstance.specs.forEach(spec => {
        spec.specValueList.forEach(value => {
          if (value.checkState === '1') {
            // 保持当前选中状态
          } else {
            // 其他都设为禁用
            value.checkState = '3';
          }
        });
      });
    }

    console.log('✅ 模拟完成！');
    console.log('📊 当前状态：');
    console.log('- 当前SKU库存:', pageInstance.defaultProduct.productStock);
    console.log('- hasAnyStock 应该返回:', pageInstance.hasAnyStock);
    console.log('- 底部按钮应该显示"库存不足"');
    console.log('- 规格弹框应该显示"库存不足"');
    
    // 返回恢复函数
    return function restore() {
      pageInstance.defaultProduct = originalDefaultProduct;
      pageInstance.specs = originalSpecs;
      console.log('🔄 已恢复原始数据');
    };
    
  } catch (error) {
    console.error('❌ 模拟失败:', error);
  }
}

// 模拟场景3：单SKU商品库存为0
function simulateSingleSkuNoStock() {
  console.log('🧪 开始模拟：单SKU商品库存为0的情况');
  
  try {
    const pageInstance = getCurrentPageVue();
    if (!pageInstance) {
      console.error('❌ 无法获取页面Vue实例');
      return;
    }

    // 备份原始数据
    const originalDefaultProduct = JSON.parse(JSON.stringify(pageInstance.defaultProduct));
    const originalSpecs = JSON.parse(JSON.stringify(pageInstance.specs));
    
    // 设置当前SKU库存为0
    pageInstance.defaultProduct.productStock = 0;
    
    // 清空规格（模拟单SKU商品）
    pageInstance.specs = [];

    console.log('✅ 模拟完成！');
    console.log('📊 当前状态：');
    console.log('- 当前SKU库存:', pageInstance.defaultProduct.productStock);
    console.log('- 规格数量:', pageInstance.specs.length);
    console.log('- hasAnyStock 应该返回:', pageInstance.hasAnyStock);
    console.log('- 底部按钮应该显示"库存不足"');
    
    // 返回恢复函数
    return function restore() {
      pageInstance.defaultProduct = originalDefaultProduct;
      pageInstance.specs = originalSpecs;
      console.log('🔄 已恢复原始数据');
    };
    
  } catch (error) {
    console.error('❌ 模拟失败:', error);
  }
}

// 检查当前页面状态
function checkCurrentState() {
  console.log('🔍 检查当前页面状态');
  
  try {
    const pageInstance = getCurrentPageVue();
    if (!pageInstance) {
      console.error('❌ 无法获取页面Vue实例');
      return;
    }

    console.log('📊 当前页面数据：');
    console.log('- 商品ID:', pageInstance.goodsId);
    console.log('- 产品ID:', pageInstance.productId);
    console.log('- 商品名称:', pageInstance.goodsData?.goodsName);
    console.log('- 当前SKU库存:', pageInstance.defaultProduct?.productStock);
    console.log('- 规格数量:', pageInstance.specs?.length || 0);
    console.log('- hasAnyStock:', pageInstance.hasAnyStock);
    
    if (pageInstance.specs && pageInstance.specs.length > 0) {
      console.log('- 规格详情:');
      pageInstance.specs.forEach((spec, index) => {
        console.log(`  ${index + 1}. ${spec.specName}:`);
        spec.specValueList.forEach(value => {
          const status = value.checkState === '1' ? '选中' : 
                        value.checkState === '2' ? '可选' : '禁用';
          console.log(`     - ${value.specValue}: ${status}`);
        });
      });
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  }
}

// 主要的测试函数
function runStockTest() {
  console.log('🚀 开始库存测试');
  console.log('请确保已在商品详情页面运行此脚本');
  console.log('');
  console.log('可用的测试函数：');
  console.log('1. checkCurrentState() - 检查当前页面状态');
  console.log('2. simulatePartialStock() - 模拟部分SKU有库存');
  console.log('3. simulateNoStock() - 模拟所有SKU都没库存');
  console.log('4. simulateSingleSkuNoStock() - 模拟单SKU无库存');
  console.log('');
  console.log('示例用法：');
  console.log('const restore = simulatePartialStock(); // 运行测试');
  console.log('// 测试完成后恢复原始数据');
  console.log('restore();');
}

// 自动运行
console.log('📦 库存测试脚本已加载');
console.log('运行 runStockTest() 开始测试');

// 导出函数到全局作用域
window.stockTest = {
  runStockTest,
  checkCurrentState,
  simulatePartialStock,
  simulateNoStock,
  simulateSingleSkuNoStock
};
