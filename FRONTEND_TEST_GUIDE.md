# 前端库存测试指南

## 测试目标
验证修复后的SKU库存逻辑是否正常工作，特别是当某个SKU库存为0时，用户是否能正常切换到其他有库存的SKU。

## 测试环境
- 商品链接：`standard/product/detail?productId=200002310010&goodsId=100002140037&u=MTY1`
- 商品名称：线下活动|北京元宇宙青年汇
- 原始库存：10

## 测试方法

### 方法1：快速自动测试（推荐）

1. **打开商品详情页**
   ```
   standard/product/detail?productId=200002310010&goodsId=100002140037&u=MTY1
   ```

2. **打开浏览器开发者工具**
   - 按 `F12` 或右键选择"检查"
   - 切换到 `Console` 标签

3. **运行测试脚本**
   - 复制 `quick_test.js` 文件的全部内容
   - 粘贴到控制台并按回车运行

4. **观察测试结果**
   - 脚本会自动运行两个测试场景
   - 观察页面底部按钮的变化
   - 检查控制台输出的 `hasAnyStock` 值

### 方法2：手动交互测试

1. **运行快速测试后使用手动函数**
   ```javascript
   // 设置库存为0
   manualTest.setStockZero();
   
   // 查看当前状态
   manualTest.checkStatus();
   
   // 恢复原始库存
   manualTest.restoreStock();
   ```

2. **手动操作页面**
   - 点击底部的购买按钮
   - 打开规格选择弹框
   - 尝试选择不同的规格
   - 观察按钮状态变化

### 方法3：完整功能测试

1. **使用完整模拟器**
   - 复制 `uniapp_stock_simulator.js` 内容到控制台运行
   - 使用提供的详细测试函数

2. **可用测试函数**
   ```javascript
   stockSimulator.showInfo()           // 显示当前商品信息
   stockSimulator.testPartialStock()   // 测试部分SKU有库存
   stockSimulator.testNoStock()        // 测试所有SKU无库存
   stockSimulator.testSingleSkuNoStock() // 测试单SKU无库存
   stockSimulator.checkHasAnyStock()   // 检查hasAnyStock值
   ```

## 测试场景

### 场景1：部分SKU有库存 ✅
**设置：**
- 当前选中SKU库存：0
- 其他SKU状态：可选（有库存）

**预期结果：**
- `hasAnyStock` 返回 `true`
- 底部按钮显示正常购买选项（不显示"库存不足"）
- 规格弹框可以正常打开
- 规格弹框内显示"当前规格库存不足，请选择其他规格"
- 用户可以选择其他规格

### 场景2：所有SKU都无库存 ✅
**设置：**
- 当前选中SKU库存：0
- 其他SKU状态：禁用（无库存）

**预期结果：**
- `hasAnyStock` 返回 `false`
- 底部按钮显示"库存不足"
- 规格弹框内显示"库存不足"
- 所有规格选项显示为禁用状态

### 场景3：单SKU商品无库存 ✅
**设置：**
- 当前SKU库存：0
- 规格数量：0（无规格选择）

**预期结果：**
- `hasAnyStock` 返回 `false`
- 底部按钮显示"库存不足"
- 没有规格选择选项

## 验证要点

### 1. 底部按钮状态
- **修复前**：只要当前SKU库存为0就显示"库存不足"
- **修复后**：只有所有SKU都无库存才显示"库存不足"

### 2. 规格选择功能
- **修复前**：当前SKU无库存时无法正常选择其他规格
- **修复后**：可以正常切换到有库存的其他规格

### 3. 用户提示信息
- **修复前**：统一显示"库存不足"
- **修复后**：区分"当前规格库存不足，请选择其他规格"和"库存不足"

### 4. hasAnyStock计算属性
```javascript
// 正确的逻辑
hasAnyStock() {
  if (!this.specs || this.specs.length === 0) {
    return this.defaultProduct && this.defaultProduct.productStock > 0;
  }
  
  const hasAvailableSpecs = this.specs.some(spec => 
    spec.specValueList.some(value => value.checkState === '2')
  );
  const currentHasStock = this.defaultProduct && this.defaultProduct.productStock > 0;
  
  return hasAvailableSpecs || currentHasStock;
}
```

## 常见问题

### Q: 脚本运行后没有效果？
A: 确保在正确的商品详情页运行，并且页面已完全加载。

### Q: 控制台报错"getCurrentPages is not a function"？
A: 这是正常的，脚本会尝试多种方法获取页面实例。

### Q: 页面没有立即更新？
A: 脚本会调用 `$forceUpdate()` 强制更新，如果仍无效果，请刷新页面重试。

### Q: 如何恢复原始数据？
A: 快速测试脚本会自动恢复，手动测试可以调用 `restore()` 函数或刷新页面。

## 测试报告模板

```
测试时间：[填写时间]
测试环境：[浏览器类型和版本]
商品链接：standard/product/detail?productId=200002310010&goodsId=100002140037&u=MTY1

场景1 - 部分SKU有库存：
- hasAnyStock值：[true/false]
- 底部按钮状态：[正常/库存不足]
- 规格选择：[正常/异常]
- 结果：[通过/失败]

场景2 - 所有SKU无库存：
- hasAnyStock值：[true/false]
- 底部按钮状态：[正常/库存不足]
- 规格选择：[正常/异常]
- 结果：[通过/失败]

场景3 - 单SKU无库存：
- hasAnyStock值：[true/false]
- 底部按钮状态：[正常/库存不足]
- 结果：[通过/失败]

总体评价：[通过/失败]
备注：[其他发现的问题]
```
